<?php
require_once 'includes/config.php';

echo "<h2>ZARA-Events Database Connection Test</h2>";

// Check if initialization is requested
if (isset($_GET['init']) && $_GET['init'] === 'database') {
    echo "<h3>🔧 Initializing Database...</h3>";

    // Initialize database schema
    $schemaFile = 'database/schema.sql';
    if (file_exists($schemaFile)) {
        $schema = file_get_contents($schemaFile);
        $statements = array_filter(array_map('trim', explode(';', $schema)));

        foreach ($statements as $statement) {
            if (!empty($statement) && !preg_match('/^--/', $statement)) {
                try {
                    $db->query($statement);
                    $db->execute();
                } catch (Exception $e) {
                    echo "<p style='color: orange;'>⚠️ " . htmlspecialchars($e->getMessage()) . "</p>";
                }
            }
        }
        echo "<p style='color: green;'>✅ Database schema initialized</p>";
    }

    // Initialize data
    $dataFile = 'database/init-data.sql';
    if (file_exists($dataFile)) {
        $initData = file_get_contents($dataFile);
        $statements = array_filter(array_map('trim', explode(';', $initData)));

        foreach ($statements as $statement) {
            if (!empty($statement) && !preg_match('/^--/', $statement)) {
                try {
                    $db->query($statement);
                    $db->execute();
                } catch (Exception $e) {
                    echo "<p style='color: orange;'>⚠️ " . htmlspecialchars($e->getMessage()) . "</p>";
                }
            }
        }
        echo "<p style='color: green;'>✅ Initial data loaded</p>";
    }

    // Fix user passwords
    $adminHash = password_hash('admin123', PASSWORD_DEFAULT);
    $db->query("UPDATE users SET password = :password WHERE username = 'admin'");
    $db->bind(':password', $adminHash);
    $db->execute();

    $userHash = password_hash('user123', PASSWORD_DEFAULT);
    $db->query("UPDATE users SET password = :password WHERE username = 'testuser'");
    $db->bind(':password', $userHash);
    $db->execute();

    echo "<p style='color: green;'>✅ User passwords updated</p>";
    echo "<p><strong>Admin:</strong> admin / admin123</p>";
    echo "<p><strong>User:</strong> testuser / user123</p>";
}

try {
    // Test basic connection
    echo "<h3>✅ Database Connection: SUCCESS</h3>";
    echo "<p>Connected to database: <strong>" . DB_NAME . "</strong></p>";
    echo "<p>Host: <strong>" . DB_HOST . "</strong></p>";
    echo "<p>User: <strong>" . DB_USER . "</strong></p>";

    // Test tables exist
    echo "<h3>Database Tables:</h3>";
    $db->query("SHOW TABLES");
    $tables = $db->resultset();

    if (empty($tables)) {
        echo "<p style='color: red;'>❌ No tables found! Database may not be initialized.</p>";
        echo "<p><a href='?init=database' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔧 Initialize Database Now</a></p>";

        // Also add more comprehensive initialization
        if (isset($_GET['force_init'])) {
            echo "<h3>🔧 Force Initializing Database...</h3>";

            // Create all tables with complete structure
            $createStatements = [
                "CREATE TABLE IF NOT EXISTS users (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    username VARCHAR(50) UNIQUE NOT NULL,
                    email VARCHAR(100) UNIQUE NOT NULL,
                    password VARCHAR(255) NOT NULL,
                    first_name VARCHAR(50) NOT NULL,
                    last_name VARCHAR(50) NOT NULL,
                    phone VARCHAR(20),
                    address TEXT,
                    role ENUM('user', 'admin') DEFAULT 'user',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )",

                "CREATE TABLE IF NOT EXISTS events (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    title VARCHAR(200) NOT NULL,
                    description TEXT,
                    event_date DATE NOT NULL,
                    event_time TIME NOT NULL,
                    venue VARCHAR(200) NOT NULL,
                    location VARCHAR(200) NOT NULL,
                    organizer VARCHAR(100) NOT NULL,
                    organizer_contact VARCHAR(100),
                    image_url VARCHAR(500),
                    price DECIMAL(10, 2) NOT NULL,
                    total_tickets INT NOT NULL,
                    available_tickets INT NOT NULL,
                    category VARCHAR(50),
                    status ENUM('active', 'inactive', 'cancelled') DEFAULT 'active',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )",

                "CREATE TABLE IF NOT EXISTS bookings (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    event_id INT NOT NULL,
                    quantity INT NOT NULL,
                    total_amount DECIMAL(10, 2) NOT NULL,
                    booking_status ENUM('pending', 'confirmed', 'cancelled') DEFAULT 'pending',
                    payment_status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
                    booking_reference VARCHAR(50) UNIQUE NOT NULL,
                    attendee_name VARCHAR(100) NOT NULL,
                    attendee_email VARCHAR(100) NOT NULL,
                    attendee_phone VARCHAR(20),
                    special_requirements TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )",

                "CREATE TABLE IF NOT EXISTS cart (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    event_id INT NOT NULL,
                    quantity INT NOT NULL,
                    added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )",

                "CREATE TABLE IF NOT EXISTS user_sessions (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    session_token VARCHAR(255) NOT NULL,
                    expires_at TIMESTAMP NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )",

                "CREATE TABLE IF NOT EXISTS password_reset_tokens (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    token VARCHAR(255) NOT NULL,
                    expires_at TIMESTAMP NOT NULL,
                    used BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )",

                "CREATE TABLE IF NOT EXISTS payments (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    booking_id INT NOT NULL,
                    payment_reference VARCHAR(255) NOT NULL UNIQUE,
                    payment_method ENUM('mobile_money', 'bank_transfer', 'cash', 'card') NOT NULL,
                    amount DECIMAL(10, 2) NOT NULL,
                    currency VARCHAR(3) DEFAULT 'XAF',
                    payment_status ENUM('pending', 'processing', 'completed', 'failed', 'refunded') DEFAULT 'pending',
                    transaction_id VARCHAR(255),
                    payment_gateway VARCHAR(50) DEFAULT 'simulation',
                    payment_details JSON,
                    processed_at TIMESTAMP NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                )"
            ];

            foreach ($createStatements as $sql) {
                try {
                    $db->query($sql);
                    $db->execute();
                    echo "<p style='color: green;'>✅ Table created successfully</p>";
                } catch (Exception $e) {
                    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
                }
            }

            // Insert users with correct password hashes
            try {
                $adminHash = password_hash('admin123', PASSWORD_DEFAULT);
                $db->query("INSERT IGNORE INTO users (username, email, password, first_name, last_name, role) VALUES ('admin', '<EMAIL>', :password, 'System', 'Administrator', 'admin')");
                $db->bind(':password', $adminHash);
                $db->execute();
                echo "<p style='color: green;'>✅ Admin user created (admin/admin123)</p>";

                $userHash = password_hash('user123', PASSWORD_DEFAULT);
                $db->query("INSERT IGNORE INTO users (username, email, password, first_name, last_name, role) VALUES ('testuser', '<EMAIL>', :password, 'Test', 'User', 'user')");
                $db->bind(':password', $userHash);
                $db->execute();
                echo "<p style='color: green;'>✅ Test user created (testuser/user123)</p>";
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Error creating users: " . htmlspecialchars($e->getMessage()) . "</p>";
            }

            // Insert sample events
            $events = [
                ['Tech Conference 2024', 'Annual technology conference featuring latest innovations in AI, blockchain, and cloud computing.', '2024-03-15', '09:00:00', 'Convention Center', 'Douala, Cameroon', 'Tech Events Inc', '<EMAIL>', 175000, 500, 500, 'Technology', 'https://images.unsplash.com/photo-1540575467063-178a50c2df87?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'],
                ['Music Festival Summer', 'Three-day music festival featuring top artists from around the world.', '2024-06-20', '18:00:00', 'Central Park', 'Yaoundé, Cameroon', 'Music Productions', '<EMAIL>', 87500, 1000, 1000, 'Music', 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'],
                ['Business Workshop', 'Professional development workshop for entrepreneurs and business leaders.', '2024-04-10', '10:00:00', 'Business Center', 'Libreville, Gabon', 'Business Academy', '<EMAIL>', 58500, 100, 100, 'Business', 'https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80']
            ];

            foreach ($events as $event) {
                try {
                    $db->query("INSERT IGNORE INTO events (title, description, event_date, event_time, venue, location, organizer, organizer_contact, price, total_tickets, available_tickets, category, image_url) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                    $stmt = $db->getConnection()->prepare("INSERT IGNORE INTO events (title, description, event_date, event_time, venue, location, organizer, organizer_contact, price, total_tickets, available_tickets, category, image_url) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                    $stmt->execute($event);
                    echo "<p style='color: green;'>✅ Event added: " . htmlspecialchars($event[0]) . "</p>";
                } catch (Exception $e) {
                    echo "<p style='color: orange;'>⚠️ Event may already exist: " . htmlspecialchars($event[0]) . "</p>";
                }
            }

            echo "<h3 style='color: green;'>🎉 Database Initialization Complete!</h3>";
            echo "<p><a href='?' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔄 Refresh Page</a></p>";
        } else {
            echo "<p><a href='?force_init=true' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-left: 10px;'>🚀 Force Initialize Database</a></p>";
        }
    } else {
        echo "<ul>";
        foreach ($tables as $table) {
            $tableName = array_values((array)$table)[0];
            echo "<li>✅ " . $tableName . "</li>";
        }
        echo "</ul>";
    }

    // Test some basic queries
    echo "<h3>Sample Data:</h3>";

    // Count users
    $db->query("SELECT COUNT(*) as count FROM users");
    $userCount = $db->single();
    echo "<p>👥 Users: <strong>" . ($userCount->count ?? 0) . "</strong></p>";

    // Count events
    $db->query("SELECT COUNT(*) as count FROM events");
    $eventCount = $db->single();
    echo "<p>📅 Events: <strong>" . ($eventCount->count ?? 0) . "</strong></p>";

    // Count bookings
    $db->query("SELECT COUNT(*) as count FROM bookings");
    $bookingCount = $db->single();
    echo "<p>🎫 Bookings: <strong>" . ($bookingCount->count ?? 0) . "</strong></p>";

    echo "<h3>✅ All database tests passed!</h3>";
    echo "<p><a href='welcome.php'>Go to Welcome Page</a> | <a href='index.php'>Browse Events</a></p>";
    echo "<p><strong>New Port:</strong> Application now running on port 7823</p>";

} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ Database Error:</h3>";
    echo "<p style='color: red;'>" . htmlspecialchars($e->getMessage()) . "</p>";

    echo "<h3>Troubleshooting:</h3>";
    echo "<ul>";
    echo "<li>Make sure Docker containers are running: <code>docker ps</code></li>";
    echo "<li>Check MySQL container logs: <code>docker logs event_booking_mysql</code></li>";
    echo "<li>Verify database initialization files are loaded</li>";
    echo "</ul>";
}
?>
